import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:unstack/logic/task/task_impl.dart';
import 'package:unstack/models/task.model.dart';

void main() {
  // Initialize FFI for testing
  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  group('TaskManager Tests', () {
    late TaskManager taskManager;

    setUp(() async {
      taskManager = TaskManager();
      // Clean up any existing data before each test
      await taskManager.deleteAllTasks();
    });

    tearDown(() async {
      await taskManager.close();
    });

    test('should add and retrieve a task', () async {
      // Arrange
      final task = Task(
        id: 'test-1',
        title: 'Test Task',
        description: 'This is a test task',
        priority: TaskPriority.medium,
        createdAt: DateTime.now(),
      );

      // Act
      await taskManager.addTask(task);
      final tasks = await taskManager.getTasks();

      // Assert
      expect(tasks.length, 1);
      expect(tasks.first.id, 'test-1');
      expect(tasks.first.title, 'Test Task');
      expect(tasks.first.priority, TaskPriority.medium);
      expect(tasks.first.isCompleted, false);
    });

    test('should update a task', () async {
      // Arrange
      final task = Task(
        id: 'test-2',
        title: 'Original Title',
        description: 'Original description',
        priority: TaskPriority.low,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task);

      final updatedTask = task.copyWith(
        title: 'Updated Title',
        priority: TaskPriority.high,
      );

      // Act
      await taskManager.updateTask(updatedTask);
      final retrievedTask = await taskManager.getTaskById('test-2');

      // Assert
      expect(retrievedTask.title, 'Updated Title');
      expect(retrievedTask.priority, TaskPriority.high);
      expect(retrievedTask.description,
          'Original description'); // Should remain unchanged
    });

    test('should mark task as completed and incomplete', () async {
      // Arrange
      final task = Task(
        id: 'test-3',
        title: 'Completion Test',
        description: 'Test completion functionality',
        priority: TaskPriority.urgent,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task);

      // Act & Assert - Mark as completed
      await taskManager.markTaskAsCompleted('test-3');
      final completedTask = await taskManager.getTaskById('test-3');
      expect(completedTask.isCompleted, true);

      // Act & Assert - Mark as incomplete
      await taskManager.markTaskAsIncomplete('test-3');
      final incompleteTask = await taskManager.getTaskById('test-3');
      expect(incompleteTask.isCompleted, false);
    });

    test('should delete a task', () async {
      // Arrange
      final task = Task(
        id: 'test-4',
        title: 'Delete Test',
        description: 'This task will be deleted',
        priority: TaskPriority.medium,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task);
      final tasksBeforeDelete = await taskManager.getTasks();
      expect(tasksBeforeDelete.length, 1);

      // Act
      await taskManager.deleteTask('test-4');

      // Assert
      final tasksAfterDelete = await taskManager.getTasks();
      expect(tasksAfterDelete.length, 0);
    });

    test('should get completed tasks only', () async {
      // Arrange
      final task1 = Task(
        id: 'test-5',
        title: 'Incomplete Task',
        description: 'This task is not completed',
        priority: TaskPriority.low,
        createdAt: DateTime.now(),
      );

      final task2 = Task(
        id: 'test-6',
        title: 'Complete Task',
        description: 'This task is completed',
        priority: TaskPriority.high,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task1);
      await taskManager.addTask(task2);
      await taskManager.markTaskAsCompleted('test-6');

      // Act
      final completedTasks = await taskManager.getCompletedTasks();

      // Assert
      expect(completedTasks.length, 1);
      expect(completedTasks.first.id, 'test-6');
      expect(completedTasks.first.isCompleted, true);
    });

    test('should get task statistics', () async {
      // Arrange
      final task1 = Task(
        id: 'test-7',
        title: 'Task 1',
        description: 'Description 1',
        priority: TaskPriority.low,
        createdAt: DateTime.now(),
      );

      final task2 = Task(
        id: 'test-8',
        title: 'Task 2',
        description: 'Description 2',
        priority: TaskPriority.medium,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task1);
      await taskManager.addTask(task2);
      await taskManager.markTaskAsCompleted('test-7');

      // Act
      final stats = await taskManager.getTaskStatistics();

      // Assert
      expect(stats['total'], 2);
      expect(stats['completed'], 1);
      expect(stats['pending'], 1);
      expect(stats['completionPercentage'], 50);
    });

    test('should search tasks by title and description', () async {
      // Arrange
      final task1 = Task(
        id: 'test-9',
        title: 'Flutter Development',
        description: 'Work on mobile app',
        priority: TaskPriority.high,
        createdAt: DateTime.now(),
      );

      final task2 = Task(
        id: 'test-10',
        title: 'Backend API',
        description: 'Develop Flutter integration',
        priority: TaskPriority.medium,
        createdAt: DateTime.now(),
      );

      final task3 = Task(
        id: 'test-11',
        title: 'Database Design',
        description: 'Design schema for users',
        priority: TaskPriority.low,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task1);
      await taskManager.addTask(task2);
      await taskManager.addTask(task3);

      // Act
      final flutterTasks = await taskManager.searchTasks('Flutter');

      // Assert
      expect(flutterTasks.length,
          2); // Should find both tasks containing "Flutter"
      expect(flutterTasks.any((task) => task.id == 'test-9'), true);
      expect(flutterTasks.any((task) => task.id == 'test-10'), true);
    });

    test('should delete all tasks', () async {
      // Arrange
      final task1 = Task(
        id: 'test-12',
        title: 'Task 1',
        description: 'Description 1',
        priority: TaskPriority.low,
        createdAt: DateTime.now(),
      );

      final task2 = Task(
        id: 'test-13',
        title: 'Task 2',
        description: 'Description 2',
        priority: TaskPriority.high,
        createdAt: DateTime.now(),
      );

      await taskManager.addTask(task1);
      await taskManager.addTask(task2);

      final tasksBeforeDelete = await taskManager.getTasks();
      expect(tasksBeforeDelete.length, 2);

      // Act
      await taskManager.deleteAllTasks();

      // Assert
      final tasksAfterDelete = await taskManager.getTasks();
      expect(tasksAfterDelete.length, 0);
    });
  });
}
