import 'dart:math';

import 'package:confetti/confetti.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:unstack/models/streak_data.dart';
import 'package:unstack/providers/task_provider.dart';

import 'package:unstack/routes/route.dart';

import 'package:unstack/theme/theme.dart';
import 'package:unstack/utils/app_size.dart';
import 'package:unstack/views/test_animation.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';
import 'package:unstack/widgets/streak_widget.dart';
import 'package:unstack/widgets/todo_tile.dart';
import 'package:unstack/widgets/circular_progress_3d.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _userName = '';
  late ConfettiController _confettiController;
  bool isStreak = false;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: Duration(seconds: 2));
    _loadUserName();
    _initializeStreakTracking();
  }

  Future<void> _initializeStreakTracking() async {
    await streakTracker.loadFromStorage();
    await _updateStreakData();
  }

  Future<void> _updateStreakData() async {
    final taskProvider = context.read<TaskProvider>();
    await streakTracker.updateTodayCompletion(taskProvider.tasks);
    setState(() {
      isStreak = streakTracker.currentStreak > 0;
    });
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  void showStreakOverlay(BuildContext context, int streakCount) {
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierLabel: '',
      barrierColor: Colors.black87,
      transitionDuration: Duration(milliseconds: 400),
      pageBuilder: (context, animation, secondaryAnimation) {
        return ModernStreakOverlay(
          streakCount: streakCount,
          userName: _userName,
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: Consumer<TaskProvider>(
        builder: (context, taskProvider, child) {
          final tasks = taskProvider
              .incompleteTasks; // Only show incomplete tasks in the swiper
          final allTasks = taskProvider.tasks; // All tasks for statistics

          return Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(AppSpacing.lg),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: AppSpacing.xl),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          StreakWidget(
                            currentStreak: streakTracker.currentStreak,
                          )
                              .animate()
                              .slideY(
                                begin: -0.3,
                                duration: 500.ms,
                                curve: Curves.easeOut,
                              )
                              .fadeIn(delay: 200.ms),
                          Spacer(),
                          Row(
                            children: [
                              HomeAppBarButton(
                                onPressed: () {
                                  RouteUtils.pushNamed(
                                    context,
                                    RoutePaths.profilePage,
                                  );
                                },
                                icon: CupertinoIcons.person,
                                isPremium: false,
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              HomeAppBarButton(
                                onPressed: () {
                                  RouteUtils.pushNamed(
                                    context,
                                    RoutePaths.tasksListPage,
                                  );
                                },
                                icon: CupertinoIcons.list_dash,
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              HomeAppBarButton(
                                onPressed: () {
                                  RouteUtils.pushNamed(
                                    context,
                                    RoutePaths.addTaskPage,
                                    arguments: {
                                      'fromHomePage': true,
                                    },
                                  );
                                },
                                icon: CupertinoIcons.add,
                              ),
                            ],
                          )
                              .animate()
                              .slideY(
                                begin: 0.3,
                                duration: 500.ms,
                                curve: Curves.easeOut,
                              )
                              .fadeIn(delay: 200.ms),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.md),
                      // Welcome header
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Let's get to work,",
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          )
                              .animate()
                              .slideY(
                                begin: -0.3,
                                duration: 500.ms,
                                curve: Curves.easeOut,
                              )
                              .fadeIn(),
                          Text(
                            _userName,
                            style: AppTextStyles.h1.copyWith(
                              fontSize: 28,
                              fontWeight: FontWeight.w800,
                            ),
                          )
                              .animate()
                              .slideY(
                                begin: -0.3,
                                duration: 500.ms,
                                curve: Curves.easeOut,
                              )
                              .fadeIn(delay: 200.ms),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // 3D Circular Progress Indicator
                      Center(
                        child: CircularProgressIndicator3D(
                          totalTasks: allTasks.length,
                          completedTasks: allTasks.fold<int>(
                              0,
                              (previousValue, value) =>
                                  previousValue + (value.isCompleted ? 1 : 0)),
                          size: () {
                            if (AppSize(context).height < 600) {
                              return AppSize(context).height * 0.28;
                            } else if (AppSize(context).height < 700) {
                              return AppSize(context).height * 0.30;
                            } else if (AppSize(context).height < 800) {
                              return AppSize(context).height * 0.32;
                            }
                            return AppSize(context).height * 0.32;
                          }(),
                          onTap: () {
                            Navigator.of(context).pushNamed(
                              RoutePaths.progressAnalyticsPage,
                              arguments: {
                                'tasks': allTasks,
                              },
                            );
                          },
                        ),
                      )
                          .animate()
                          .scale(
                            begin: const Offset(0.8, 0.8),
                            duration: 600.ms,
                            curve: Curves.easeOutBack,
                          )
                          .fadeIn(delay: 400.ms),

                      // Spacer(),
                      const SizedBox(height: AppSpacing.xl),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: tasks.isNotEmpty &&
                                tasks.any((e) => !e.isCompleted)
                            ? CardsSwiperWidget(
                                cardData: tasks,
                                onCardCollectionAnimationComplete: (_) {},
                                onCardChange: (index) {
                                  // Card changed to index: $index
                                },
                                cardBuilder: (context, index, visibleIndex) {
                                  var task = tasks[index];
                                  return GestureDetector(
                                    onTap: () {
                                      // Navigator.of(context).push(
                                      //   MaterialPageRoute(
                                      //     builder: (context) {
                                      //       return TaskDetailsPage(
                                      //           heroTag: 'card_$index');
                                      //     },
                                      //   ),
                                      // );
                                      // Navigator.of(context)
                                      //     .push(HeroDialogRoute(builder: (context) {
                                      //   return TaskDetailsPage(
                                      //     heroTag: 'card_$index',
                                      //   );
                                      // }));
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(32),
                                        color: AppColors.backgroundTertiary,
                                        border: Border.all(
                                          color: AppColors.textMuted,
                                          width: 1,
                                        ),
                                      ),
                                      width: double.infinity,
                                      height: () {
                                        if (AppSize(context).height < 600) {
                                          return AppSize(context).height * 0.32;
                                        } else if (AppSize(context).height <
                                            700) {
                                          return AppSize(context).height * 0.38;
                                        } else if (AppSize(context).height <
                                            800) {
                                          return AppSize(context).height * 0.34;
                                        } else {
                                          return AppSize(context).height * 0.32;
                                        }
                                      }(),
                                      padding: const EdgeInsets.only(
                                        left: AppSpacing.xl,
                                        right: AppSpacing.xl,
                                        bottom: AppSpacing.md,
                                        top: AppSpacing.xl,
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Transform.scale(
                                                scale: 2.5,
                                                child: Checkbox(
                                                  value: task.isCompleted,
                                                  visualDensity:
                                                      VisualDensity.compact,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6.0),
                                                    side: BorderSide(
                                                      color:
                                                          AppColors.whiteColor,
                                                      width: 2,
                                                    ),
                                                  ),
                                                  side: BorderSide(
                                                    color: AppColors.textMuted,
                                                    width: 2,
                                                  ),
                                                  activeColor:
                                                      AppColors.accentGreen,
                                                  onChanged:
                                                      (bool? value) async {
                                                    // Update task completion using TaskProvider
                                                    final success =
                                                        await taskProvider
                                                            .toggleTaskCompletion(
                                                                task.id);

                                                    if (success) {
                                                      // Check if all tasks are completed for streak
                                                      if (taskProvider
                                                                  .incompleteTasksCount ==
                                                              0 &&
                                                          taskProvider
                                                                  .totalTasks >
                                                              0) {
                                                        if (context.mounted) {
                                                          showStreakOverlay(
                                                            context,
                                                            streakTracker
                                                                    .currentStreak +
                                                                1,
                                                          );
                                                        }
                                                      }

                                                      if (value ?? false) {
                                                        _confettiController
                                                            .play();
                                                      }

                                                      await _updateStreakData();
                                                    }
                                                  },
                                                ),
                                              ),
                                              if (task.isCompleted)
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                    left: 24,
                                                  ),
                                                  child: Center(
                                                    child: Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons
                                                              .swipe_up_rounded,
                                                          color: AppColors
                                                              .lightWhiteColor,
                                                          size: 24,
                                                        ),
                                                        const SizedBox(
                                                          width: AppSpacing.xs,
                                                        ),
                                                        Text(
                                                          'Swipe up for next task',
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            color: AppColors
                                                                .lightWhiteColor,
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          const SizedBox(height: AppSpacing.md),
                                          Text(task.title,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              style: () {
                                            if (AppSize(context).height < 600) {
                                              return AppTextStyles.h2.copyWith(
                                                color: AppColors.textPrimary,
                                                fontSize: 18,
                                              );
                                            } else if (AppSize(context).height <
                                                700) {
                                              return AppTextStyles.h2.copyWith(
                                                color: AppColors.textPrimary,
                                                fontSize: 20,
                                              );
                                            } else if (AppSize(context).height <
                                                800) {
                                              return AppTextStyles.h2.copyWith(
                                                color: AppColors.textPrimary,
                                                fontSize: 24,
                                              );
                                            } else {
                                              return AppTextStyles.h2.copyWith(
                                                color: AppColors.textPrimary,
                                                fontSize: 28,
                                              );
                                            }
                                          }()),
                                          const SizedBox(
                                            height: AppSpacing.xs,
                                          ),
                                          Text(
                                            task.description,
                                            maxLines: () {
                                              if (AppSize(context).height <
                                                  600) {
                                                return 1;
                                              } else if (AppSize(context)
                                                      .height <
                                                  700) {
                                                return 1;
                                              } else if (AppSize(context)
                                                      .height <
                                                  800) {
                                                return 1;
                                              } else {
                                                return 3;
                                              }
                                            }(),
                                            overflow: TextOverflow.ellipsis,
                                            style: AppTextStyles.bodySmall
                                                .copyWith(
                                              color: AppColors.textSecondary,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const Spacer(),
                                          Row(
                                            children: [
                                              Icon(
                                                CupertinoIcons.calendar,
                                                color: AppColors.textPrimary,
                                                size: 20,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                task.isDueToday
                                                    ? 'Due Today'
                                                    : task.isDueSoon
                                                        ? 'Due Soon'
                                                        : 'Not Due',
                                                style: AppTextStyles.bodyLarge
                                                    .copyWith(
                                                  color: AppColors.textPrimary,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              const Spacer(),
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: AppSpacing.sm,
                                                  vertical: AppSpacing.xs,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: task.priority.color
                                                      .withValues(alpha: 0.2),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          AppBorderRadius.full),
                                                  border: Border.all(
                                                    color: task.priority.color
                                                        .withValues(alpha: 0.5),
                                                    width: 1,
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons.flag,
                                                      size: 12,
                                                      color:
                                                          task.priority.color,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      task.priority.label,
                                                      style: AppTextStyles
                                                          .caption
                                                          .copyWith(
                                                        color:
                                                            task.priority.color,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: AppSpacing.xs,
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              )
                            : Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color: AppColors.backgroundTertiary,
                                  border: Border.all(
                                    color: AppColors.textMuted,
                                    width: 0.5,
                                  ),
                                ),
                                width: 600,
                                height: 280,
                                padding: const EdgeInsets.all(AppSpacing.xl),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      tasks.isEmpty
                                          ? 'Please add some task'
                                          : 'Hoorey you finished with all of your tasks for today!',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppTextStyles.h2.copyWith(
                                        color: AppColors.textPrimary,
                                        fontSize: 32,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: AppSpacing.xs,
                                    ),
                                    Text(
                                      'Click on button at top right to add some tasks!',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppTextStyles.bodySmall.copyWith(
                                        color: AppColors.textSecondary,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      )
                          .animate()
                          .slideY(
                            begin: 0.3,
                            duration: 500.ms,
                            curve: Curves.easeOut,
                          )
                          .fadeIn(delay: 200.ms),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.topCenter,
                child: ConfettiWidget(
                  confettiController: _confettiController,
                  blastDirectionality: BlastDirectionality.explosive,
                  blastDirection: pi * 0.5, maxBlastForce: 10,
                  numberOfParticles: 50,
                  colors: const [
                    AppColors.accentPurple,
                    AppColors.accentBlue,
                    AppColors.accentGreen,
                    AppColors.accentYellow,
                    AppColors.accentPink,
                    AppColors.accentOrange,
                  ], // manually specify the colors to be used
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
