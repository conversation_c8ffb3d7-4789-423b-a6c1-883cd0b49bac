import 'package:sqflite/sqflite.dart';
import 'package:unstack/logic/task/task_contract.dart';
import 'package:unstack/logic/sql/database_helper.dart';
import 'package:unstack/models/task.model.dart';

class TaskManager implements ITaskManagerContract {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  @override
  Future<void> addTask(Task task) async {
    try {
      final db = await _databaseHelper.database;
      final taskMap = _databaseHelper.taskToMap(task);

      // Get the current max sort_order and increment it
      final result = await db.rawQuery(
          'SELECT MAX(sort_order) as max_order FROM tasks WHERE is_completed = 0');
      final maxOrder = result.first['max_order'] as int? ?? 0;
      taskMap['sort_order'] = maxOrder + 1;

      await db.insert(
        'tasks',
        taskMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to add task: $e');
    }
  }

  @override
  Future<void> updateTask(Task task) async {
    try {
      final db = await _databaseHelper.database;
      final taskMap = _databaseHelper.taskToMap(task);

      await db.update(
        'tasks',
        taskMap,
        where: 'id = ?',
        whereArgs: [task.id],
      );
    } catch (e) {
      throw Exception('Failed to update task: $e');
    }
  }

  @override
  Future<void> deleteTask(String taskId) async {
    try {
      final db = await _databaseHelper.database;
      await db.delete(
        'tasks',
        where: 'id = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      throw Exception('Failed to delete task: $e');
    }
  }

  @override
  Future<List<Task>> getTasks() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        orderBy: 'is_completed ASC, sort_order ASC, created_at DESC',
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to get tasks: $e');
    }
  }

  @override
  Future<List<Task>> getCompletedTasks() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'is_completed = ?',
        whereArgs: [1],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to get completed tasks: $e');
    }
  }

  @override
  Future<Task> getTaskById(String taskId) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'id = ?',
        whereArgs: [taskId],
        limit: 1,
      );

      if (maps.isEmpty) {
        throw Exception('Task not found with id: $taskId');
      }

      return _databaseHelper.mapToTask(maps.first);
    } catch (e) {
      throw Exception('Failed to get task by id: $e');
    }
  }

  @override
  Future<void> markTaskAsCompleted(String taskId) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'tasks',
        {'is_completed': 1},
        where: 'id = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      throw Exception('Failed to mark task as completed: $e');
    }
  }

  @override
  Future<void> markTaskAsIncomplete(String taskId) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'tasks',
        {'is_completed': 0},
        where: 'id = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      throw Exception('Failed to mark task as incomplete: $e');
    }
  }

  @override
  Future<void> deleteAllTasks() async {
    try {
      final db = await _databaseHelper.database;
      await db.delete('tasks');
    } catch (e) {
      throw Exception('Failed to delete all tasks: $e');
    }
  }

  @override
  Future<void> sortPriorityTasks(Task task, int newIndex) async {
    try {
      final db = await _databaseHelper.database;

      // Get all incomplete tasks ordered by current sort_order
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'is_completed = ?',
        whereArgs: [0],
        orderBy: 'sort_order ASC',
      );

      final tasks = maps.map((map) => _databaseHelper.mapToTask(map)).toList();

      // Remove the task from its current position
      tasks.removeWhere((t) => t.id == task.id);

      // Insert the task at the new position
      if (newIndex >= 0 && newIndex <= tasks.length) {
        tasks.insert(newIndex, task);
      } else {
        tasks.add(task); // Add to end if index is out of bounds
      }

      // Update sort_order for all tasks
      await db.transaction((txn) async {
        for (int i = 0; i < tasks.length; i++) {
          await txn.update(
            'tasks',
            {'sort_order': i},
            where: 'id = ?',
            whereArgs: [tasks[i].id],
          );
        }
      });
    } catch (e) {
      throw Exception('Failed to sort priority tasks: $e');
    }
  }

  // Additional helper methods for better functionality

  /// Get tasks filtered by completion status
  Future<List<Task>> getTasksByStatus({required bool isCompleted}) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'is_completed = ?',
        whereArgs: [isCompleted ? 1 : 0],
        orderBy:
            isCompleted ? 'created_at DESC' : 'sort_order ASC, created_at DESC',
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to get tasks by status: $e');
    }
  }

  /// Get tasks filtered by priority
  Future<List<Task>> getTasksByPriority(TaskPriority priority) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'priority = ?',
        whereArgs: [priority.index],
        orderBy: 'is_completed ASC, sort_order ASC, created_at DESC',
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to get tasks by priority: $e');
    }
  }

  /// Get tasks sorted by different criteria
  Future<List<Task>> getTasksSorted(TaskSortOption sortOption,
      {bool ascending = true}) async {
    try {
      final db = await _databaseHelper.database;
      String orderBy;

      switch (sortOption) {
        case TaskSortOption.dateCreated:
          orderBy = ascending ? 'created_at ASC' : 'created_at DESC';
          break;
        case TaskSortOption.priority:
          orderBy = ascending ? 'priority ASC' : 'priority DESC';
          break;
        case TaskSortOption.alphabetical:
          orderBy = ascending ? 'title ASC' : 'title DESC';
          break;
      }

      // Always show incomplete tasks first, then completed
      orderBy = 'is_completed ASC, $orderBy';

      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        orderBy: orderBy,
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to get sorted tasks: $e');
    }
  }

  /// Get task statistics
  Future<Map<String, int>> getTaskStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final totalResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM tasks');
      final completedResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM tasks WHERE is_completed = 1');
      final pendingResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM tasks WHERE is_completed = 0');

      final total = totalResult.first['count'] as int;
      final completed = completedResult.first['count'] as int;
      final pending = pendingResult.first['count'] as int;

      return {
        'total': total,
        'completed': completed,
        'pending': pending,
        'completionPercentage':
            total > 0 ? ((completed / total) * 100).round() : 0,
      };
    } catch (e) {
      throw Exception('Failed to get task statistics: $e');
    }
  }

  /// Search tasks by title or description
  Future<List<Task>> searchTasks(String query) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tasks',
        where: 'title LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'is_completed ASC, sort_order ASC, created_at DESC',
      );

      return maps.map((map) => _databaseHelper.mapToTask(map)).toList();
    } catch (e) {
      throw Exception('Failed to search tasks: $e');
    }
  }

  /// Close database connection
  Future<void> close() async {
    await _databaseHelper.close();
  }
}
