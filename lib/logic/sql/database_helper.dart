import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:unstack/models/task.model.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'unstack_tasks.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        priority INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        due_date INTEGER,
        is_completed INTEGER NOT NULL DEFAULT 0,
        sort_order INTEGER DEFAULT 0
      )
    ''');

    // Create index for better query performance
    await db.execute('CREATE INDEX idx_tasks_completed ON tasks(is_completed)');
    await db.execute('CREATE INDEX idx_tasks_priority ON tasks(priority)');
    await db.execute('CREATE INDEX idx_tasks_created_at ON tasks(created_at)');
    await db.execute('CREATE INDEX idx_tasks_sort_order ON tasks(sort_order)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here if needed in the future
    if (oldVersion < newVersion) {
      // Add migration logic here
    }
  }

  // Convert Task model to Map for database storage
  Map<String, dynamic> taskToMap(Task task) {
    return {
      'id': task.id,
      'title': task.title,
      'description': task.description,
      'priority': task.priority.index,
      'created_at': task.createdAt.millisecondsSinceEpoch,
      'due_date': task.dueDate?.millisecondsSinceEpoch,
      'is_completed': task.isCompleted ? 1 : 0,
      'sort_order': 0, // Default sort order, can be updated later
    };
  }

  // Convert Map from database to Task model
  Task mapToTask(Map<String, dynamic> map) {
    return Task(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      priority: TaskPriority.values[map['priority'] as int],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      dueDate: map['due_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['due_date'] as int)
          : null,
      isCompleted: (map['is_completed'] as int) == 1,
    );
  }

  // Close database connection
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // Delete database (useful for testing or reset functionality)
  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'unstack_tasks.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
