import 'package:unstack/logic/auth/auth_contract.dart';

class AuthManager implements IFirebaseAuthContract {
  @override
  Future<bool> signInAsGuest() {
    // TODO: implement signInAsGuest
    throw UnimplementedError();
  }

  @override
  Future<bool> signInWithGoogle() {
    // TODO: implement signInWithGoogle
    throw UnimplementedError();
  }

  @override
  Future<bool> signOut() {
    // TODO: implement signOut
    throw UnimplementedError();
  }
}
