import 'package:flutter/foundation.dart';
import 'package:unstack/logic/task/task_impl.dart';
import 'package:unstack/models/task.model.dart';
import 'package:unstack/utils/app_logger.dart';

enum TaskState {
  initial,
  loading,
  loaded,
  error,
}

class TaskProvider extends ChangeNotifier {
  final TaskManager _taskManager = TaskManager();

  TaskState _taskState = TaskState.initial;
  List<Task> _tasks = [];
  List<Task> _completedTasks = [];
  String? _errorMessage;
  TaskSortOption _currentSortOption = TaskSortOption.dateCreated;
  bool _sortAscending = false;

  // Getters
  TaskState get taskState => _taskState;
  List<Task> get tasks => _tasks;
  List<Task> get completedTasks => _completedTasks;
  List<Task> get incompleteTasks => _tasks.where((task) => !task.isCompleted).toList();
  String? get errorMessage => _errorMessage;
  TaskSortOption get currentSortOption => _currentSortOption;
  bool get sortAscending => _sortAscending;
  bool get isLoading => _taskState == TaskState.loading;
  bool get hasError => _taskState == TaskState.error;

  // Statistics
  int get totalTasks => _tasks.length;
  int get completedTasksCount => _tasks.where((task) => task.isCompleted).length;
  int get incompleteTasksCount => _tasks.where((task) => !task.isCompleted).length;
  double get completionPercentage => 
      totalTasks > 0 ? (completedTasksCount / totalTasks) * 100 : 0.0;

  TaskProvider() {
    _initializeTasks();
  }

  Future<void> _initializeTasks() async {
    await loadTasks();
  }

  // Load all tasks
  Future<void> loadTasks() async {
    _setTaskState(TaskState.loading);

    try {
      _tasks = await _taskManager.getTasks();
      _completedTasks = await _taskManager.getCompletedTasks();
      _setTaskState(TaskState.loaded);
      AppLogger.info('Tasks loaded successfully: ${_tasks.length} total');
    } catch (e) {
      AppLogger.error('Error loading tasks: $e');
      _setError('Failed to load tasks: ${e.toString()}');
    }
  }

  // Add a new task
  Future<bool> addTask(Task task) async {
    try {
      await _taskManager.addTask(task);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task added successfully: ${task.title}');
      return true;
    } catch (e) {
      AppLogger.error('Error adding task: $e');
      _setError('Failed to add task: ${e.toString()}');
      return false;
    }
  }

  // Update an existing task
  Future<bool> updateTask(Task task) async {
    try {
      await _taskManager.updateTask(task);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task updated successfully: ${task.title}');
      return true;
    } catch (e) {
      AppLogger.error('Error updating task: $e');
      _setError('Failed to update task: ${e.toString()}');
      return false;
    }
  }

  // Delete a task
  Future<bool> deleteTask(String taskId) async {
    try {
      await _taskManager.deleteTask(taskId);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task deleted successfully: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting task: $e');
      _setError('Failed to delete task: ${e.toString()}');
      return false;
    }
  }

  // Mark task as completed
  Future<bool> markTaskAsCompleted(String taskId) async {
    try {
      await _taskManager.markTaskAsCompleted(taskId);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task marked as completed: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('Error marking task as completed: $e');
      _setError('Failed to mark task as completed: ${e.toString()}');
      return false;
    }
  }

  // Mark task as incomplete
  Future<bool> markTaskAsIncomplete(String taskId) async {
    try {
      await _taskManager.markTaskAsIncomplete(taskId);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task marked as incomplete: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('Error marking task as incomplete: $e');
      _setError('Failed to mark task as incomplete: ${e.toString()}');
      return false;
    }
  }

  // Toggle task completion status
  Future<bool> toggleTaskCompletion(String taskId) async {
    try {
      final task = _tasks.firstWhere((t) => t.id == taskId);
      if (task.isCompleted) {
        return await markTaskAsIncomplete(taskId);
      } else {
        return await markTaskAsCompleted(taskId);
      }
    } catch (e) {
      AppLogger.error('Error toggling task completion: $e');
      _setError('Failed to toggle task completion: ${e.toString()}');
      return false;
    }
  }

  // Delete all tasks
  Future<bool> deleteAllTasks() async {
    try {
      await _taskManager.deleteAllTasks();
      await loadTasks(); // Refresh the task list
      AppLogger.info('All tasks deleted successfully');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting all tasks: $e');
      _setError('Failed to delete all tasks: ${e.toString()}');
      return false;
    }
  }

  // Sort tasks
  Future<void> sortTasks(TaskSortOption sortOption, {bool? ascending}) async {
    _currentSortOption = sortOption;
    _sortAscending = ascending ?? _sortAscending;

    try {
      _tasks = await _taskManager.getTasksSorted(sortOption, ascending: _sortAscending);
      notifyListeners();
      AppLogger.info('Tasks sorted by ${sortOption.label}');
    } catch (e) {
      AppLogger.error('Error sorting tasks: $e');
      _setError('Failed to sort tasks: ${e.toString()}');
    }
  }

  // Search tasks
  Future<List<Task>> searchTasks(String query) async {
    try {
      final results = await _taskManager.searchTasks(query);
      AppLogger.info('Search completed: ${results.length} results for "$query"');
      return results;
    } catch (e) {
      AppLogger.error('Error searching tasks: $e');
      _setError('Failed to search tasks: ${e.toString()}');
      return [];
    }
  }

  // Get tasks by priority
  Future<List<Task>> getTasksByPriority(TaskPriority priority) async {
    try {
      return await _taskManager.getTasksByPriority(priority);
    } catch (e) {
      AppLogger.error('Error getting tasks by priority: $e');
      _setError('Failed to get tasks by priority: ${e.toString()}');
      return [];
    }
  }

  // Get task statistics
  Future<Map<String, int>> getTaskStatistics() async {
    try {
      return await _taskManager.getTaskStatistics();
    } catch (e) {
      AppLogger.error('Error getting task statistics: $e');
      _setError('Failed to get task statistics: ${e.toString()}');
      return {
        'total': 0,
        'completed': 0,
        'pending': 0,
        'completionPercentage': 0,
      };
    }
  }

  // Reorder tasks (for drag and drop functionality)
  Future<bool> reorderTask(Task task, int newIndex) async {
    try {
      await _taskManager.sortPriorityTasks(task, newIndex);
      await loadTasks(); // Refresh the task list
      AppLogger.info('Task reordered successfully: ${task.title}');
      return true;
    } catch (e) {
      AppLogger.error('Error reordering task: $e');
      _setError('Failed to reorder task: ${e.toString()}');
      return false;
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    if (_taskState == TaskState.error) {
      _setTaskState(TaskState.loaded);
    }
  }

  // Helper methods
  void _setTaskState(TaskState state) {
    _taskState = state;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _taskState = TaskState.error;
    notifyListeners();
  }

  @override
  void dispose() {
    _taskManager.close();
    super.dispose();
  }
}
