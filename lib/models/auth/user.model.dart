import '../subscription.model.dart';

class UserModel {
  final String username;
  final SubscriptionModel subscription;
  final DateTime? createdAt;

  UserModel({
    required this.username,
    required this.subscription,
    this.createdAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> userData) {
    if (userData['username'] == null ||
        userData['username'].toString().trim().isEmpty) {
      throw Exception('Missing required username');
    }
    final SubscriptionModel subscription = userData['subscription'] != null
        ? SubscriptionModel.fromJson(userData['subscription'])
        : SubscriptionModel();
    return UserModel(
      username: userData['username'],
      subscription: subscription,
      createdAt: userData['createdAt'] != null
          ? DateTime.parse(userData['createdAt'])
          : null,
    );
  }

  // Keep the old method for backward compatibility during transition
  factory UserModel.fromFirebase(Map<String, dynamic> userData) {
    return UserModel.from<PERSON>son(userData);
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'username': username,
      'subscription': subscription.toJson(),
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? username,
    SubscriptionModel? subscription,
    DateTime? createdAt,
  }) {
    return UserModel(
      username: username ?? this.username,
      subscription: subscription ?? this.subscription,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
