# SQLite Task Management Implementation

This document describes the complete SQLite-based task management system implemented for the Unstack Flutter app.

## Overview

The task management system consists of three main components:

1. **DatabaseHelper** (`lib/logic/sql/database_helper.dart`) - Handles SQLite database operations
2. **TaskManager** (`lib/logic/task/task_impl.dart`) - Implements the ITaskManagerContract interface
3. **TaskProvider** (`lib/providers/task_provider.dart`) - Provides state management using Provider pattern

## Features

### Core Functionality
- ✅ Create, read, update, and delete tasks
- ✅ Mark tasks as completed/incomplete
- ✅ Task sorting and filtering
- ✅ Search functionality
- ✅ Task statistics
- ✅ Priority-based organization
- ✅ Persistent local storage with SQLite

### Database Schema
```sql
CREATE TABLE tasks (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  priority INTEGER NOT NULL,
  created_at INTEGER NOT NULL,
  due_date INTEGER,
  is_completed INTEGER NOT NULL DEFAULT 0,
  sort_order INTEGER DEFAULT 0
)
```

### Indexes for Performance
- `idx_tasks_completed` - For filtering by completion status
- `idx_tasks_priority` - For priority-based queries
- `idx_tasks_created_at` - For date-based sorting
- `idx_tasks_sort_order` - For custom ordering

## Usage

### 1. Basic Setup

The TaskProvider is already configured in `main.dart`:

```dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (context) => AuthProvider()),
    ChangeNotifierProvider(create: (context) => TaskProvider()),
  ],
  child: MaterialApp(...)
)
```

### 2. Using TaskProvider in Widgets

```dart
// Access the provider
final taskProvider = context.read<TaskProvider>();

// Listen to changes
Consumer<TaskProvider>(
  builder: (context, taskProvider, child) {
    if (taskProvider.isLoading) {
      return LoadingWidget();
    }
    
    return ListView.builder(
      itemCount: taskProvider.tasks.length,
      itemBuilder: (context, index) {
        final task = taskProvider.tasks[index];
        return TaskCard(task: task);
      },
    );
  },
)
```

### 3. CRUD Operations

#### Adding a Task
```dart
final task = Task(
  id: DateTime.now().millisecondsSinceEpoch.toString(),
  title: 'New Task',
  description: 'Task description',
  priority: TaskPriority.medium,
  createdAt: DateTime.now(),
);

final success = await taskProvider.addTask(task);
```

#### Updating a Task
```dart
final updatedTask = task.copyWith(
  title: 'Updated Title',
  priority: TaskPriority.high,
);

final success = await taskProvider.updateTask(updatedTask);
```

#### Deleting a Task
```dart
final success = await taskProvider.deleteTask(taskId);
```

#### Toggle Completion
```dart
final success = await taskProvider.toggleTaskCompletion(taskId);
```

### 4. Advanced Features

#### Search Tasks
```dart
final results = await taskProvider.searchTasks('search query');
```

#### Sort Tasks
```dart
await taskProvider.sortTasks(TaskSortOption.priority, ascending: false);
```

#### Get Statistics
```dart
final stats = await taskProvider.getTaskStatistics();
print('Total: ${stats['total']}');
print('Completed: ${stats['completed']}');
print('Completion Rate: ${stats['completionPercentage']}%');
```

#### Filter by Priority
```dart
final highPriorityTasks = await taskProvider.getTasksByPriority(TaskPriority.high);
```

## State Management

### TaskProvider States
- `TaskState.initial` - Initial state before loading
- `TaskState.loading` - Loading data from database
- `TaskState.loaded` - Data successfully loaded
- `TaskState.error` - Error occurred during operation

### Available Getters
```dart
// Task lists
List<Task> get tasks              // All tasks
List<Task> get completedTasks     // Only completed tasks
List<Task> get incompleteTasks    // Only incomplete tasks

// Statistics
int get totalTasks               // Total number of tasks
int get completedTasksCount      // Number of completed tasks
int get incompleteTasksCount     // Number of incomplete tasks
double get completionPercentage  // Completion percentage

// State
bool get isLoading              // Is currently loading
bool get hasError               // Has error state
String? get errorMessage        // Current error message
```

## Error Handling

The system includes comprehensive error handling:

```dart
// Check for errors
if (taskProvider.hasError) {
  print('Error: ${taskProvider.errorMessage}');
  
  // Clear error and retry
  taskProvider.clearError();
  await taskProvider.loadTasks();
}
```

## Testing

Run the comprehensive test suite:

```bash
flutter test test/task_manager_test.dart
```

The tests cover:
- Adding and retrieving tasks
- Updating task properties
- Marking tasks as completed/incomplete
- Deleting individual and all tasks
- Getting completed tasks
- Task statistics
- Search functionality

## Example Implementation

See `lib/examples/task_provider_example.dart` for a complete example showing:
- Task list display with statistics
- Add task dialog
- Task completion toggle
- Task deletion
- Error handling
- Loading states

## Dependencies

The implementation uses these packages:
- `sqflite: ^2.4.2` - SQLite database
- `path: ^1.9.1` - Path utilities
- `provider: ^6.1.5` - State management

## Database Location

The SQLite database is stored at:
- **Android**: `/data/data/com.example.unstack/databases/unstack_tasks.db`
- **iOS**: `~/Documents/unstack_tasks.db`

## Performance Considerations

1. **Indexes** - Database includes indexes for common query patterns
2. **Batch Operations** - Uses transactions for multiple operations
3. **Lazy Loading** - Tasks are loaded on demand
4. **Efficient Queries** - Optimized SQL queries for filtering and sorting

## Migration Support

The database helper includes migration support for future schema changes:

```dart
Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
  // Handle database migrations here
  if (oldVersion < newVersion) {
    // Add migration logic here
  }
}
```

## Best Practices

1. **Always use TaskProvider** for task operations in UI
2. **Handle loading states** in your widgets
3. **Check for errors** after operations
4. **Use batch operations** when possible
5. **Test thoroughly** with the provided test suite

## Troubleshooting

### Common Issues

1. **Database not found**: Ensure proper initialization in main.dart
2. **Provider not found**: Check MultiProvider setup
3. **Tests failing**: Run `flutter pub get` and ensure test dependencies are installed

### Debug Database

To inspect the database during development:
```dart
// Get database path
final dbHelper = DatabaseHelper();
final db = await dbHelper.database;
print('Database path: ${db.path}');
```
